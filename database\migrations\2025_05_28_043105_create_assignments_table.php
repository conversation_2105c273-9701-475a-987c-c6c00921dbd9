<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assignments', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('education_id')->references('id')->on('educations')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->string('title');
            $table->text('description')->nullable();
            $table->text('guide')->nullable(); // Assignment instructions/guide
            $table->json('assignment_files')->nullable(); // Multiple file uploads
            $table->integer('time_limit')->nullable(); // Time limit in minutes
            $table->datetime('due_date')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('max_attempts')->default(1);
            $table->boolean('show_results_immediately')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assignments');
    }
};
