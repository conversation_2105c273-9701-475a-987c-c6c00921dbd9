<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssignmentQuestion extends Model
{
    use HasFactory, HasUuids;

    protected $guarded = ['id'];

    public function assignment()
    {
        return $this->belongsTo(Assignment::class);
    }

    public function options()
    {
        return $this->hasMany(AssignmentQuestionOption::class, 'question_id')->orderBy('order');
    }

    public function correctOption()
    {
        return $this->hasOne(AssignmentQuestionOption::class, 'question_id')->where('is_correct', true);
    }

    public function answers()
    {
        return $this->hasMany(AssignmentAnswer::class, 'question_id');
    }

    public function correctOptions()
    {
        return $this->hasMany(AssignmentQuestionOption::class, 'question_id')->where('is_correct', true);
    }

    public function incorrectOptions()
    {
        return $this->hasMany(AssignmentQuestionOption::class, 'question_id')->where('is_correct', false);
    }

    public function getCorrectAnswerTextAttribute()
    {
        $correctOption = $this->correctOption;
        return $correctOption ? $correctOption->option_text : null;
    }

    public function getOptionsCountAttribute()
    {
        return $this->options()->count();
    }

    public function getCorrectOptionsCountAttribute()
    {
        return $this->correctOptions()->count();
    }

    public function isValidMultipleChoice()
    {
        if ($this->type !== 'multiple_choice') {
            return true;
        }

        $optionsCount = $this->options_count;
        $correctCount = $this->correct_options_count;

        return $optionsCount >= 2 && $optionsCount <= 6 && $correctCount === 1;
    }

    public function isValidTrueFalse()
    {
        if ($this->type !== 'true_false') {
            return true;
        }

        $optionsCount = $this->options_count;
        $correctCount = $this->correct_options_count;

        return $optionsCount === 2 && $correctCount === 1;
    }

    public function isValid()
    {
        return $this->isValidMultipleChoice() && $this->isValidTrueFalse();
    }

    public function getValidationErrors()
    {
        $errors = [];

        if ($this->type === 'multiple_choice') {
            if ($this->options_count < 2) {
                $errors[] = 'Multiple choice questions must have at least 2 options.';
            }
            if ($this->options_count > 6) {
                $errors[] = 'Multiple choice questions can have at most 6 options.';
            }
            if ($this->correct_options_count === 0) {
                $errors[] = 'Multiple choice questions must have exactly one correct answer.';
            }
            if ($this->correct_options_count > 1) {
                $errors[] = 'Multiple choice questions can have only one correct answer.';
            }
        }

        if ($this->type === 'true_false') {
            if ($this->options_count !== 2) {
                $errors[] = 'True/False questions must have exactly 2 options.';
            }
            if ($this->correct_options_count !== 1) {
                $errors[] = 'True/False questions must have exactly one correct answer.';
            }
        }

        return $errors;
    }

    public function shuffleOptions()
    {
        $options = $this->options()->get()->shuffle();
        foreach ($options as $index => $option) {
            $option->update(['order' => $index + 1]);
        }
        return $this;
    }
}
