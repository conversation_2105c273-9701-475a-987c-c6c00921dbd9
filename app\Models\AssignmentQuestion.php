<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssignmentQuestion extends Model
{
    use HasFactory, HasUuids;

    protected $guarded = ['id'];

    public function assignment()
    {
        return $this->belongsTo(Assignment::class);
    }

    public function options()
    {
        return $this->hasMany(AssignmentQuestionOption::class, 'question_id')->orderBy('order');
    }

    public function correctOption()
    {
        return $this->hasOne(AssignmentQuestionOption::class, 'question_id')->where('is_correct', true);
    }

    public function answers()
    {
        return $this->hasMany(AssignmentAnswer::class, 'question_id');
    }
}
