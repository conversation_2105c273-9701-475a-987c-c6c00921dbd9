<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AssignmentAnswer extends Model
{
    use HasFactory, HasUuids;

    protected $guarded = ['id'];

    protected $casts = [
        'is_correct' => 'boolean',
    ];

    public function submission()
    {
        return $this->belongsTo(AssignmentSubmission::class, 'submission_id');
    }

    public function question()
    {
        return $this->belongsTo(AssignmentQuestion::class, 'question_id');
    }

    public function selectedOption()
    {
        return $this->belongsTo(AssignmentQuestionOption::class, 'selected_option_id');
    }
}
