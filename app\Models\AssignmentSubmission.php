<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class AssignmentSubmission extends Model
{
    use HasFactory, HasUuids;

    protected $guarded = ['id'];

    protected $casts = [
        'started_at' => 'datetime',
        'submitted_at' => 'datetime',
        'uploaded_files' => 'array',
    ];

    public function assignment()
    {
        return $this->belongsTo(Assignment::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function answers()
    {
        return $this->hasMany(AssignmentAnswer::class, 'submission_id');
    }

    public function isTimeUp()
    {
        if (!$this->assignment->time_limit) {
            return false;
        }

        $timeLimit = $this->assignment->time_limit; // in minutes
        $timeElapsed = Carbon::now()->diffInMinutes($this->started_at);

        return $timeElapsed >= $timeLimit;
    }

    public function getRemainingTimeAttribute()
    {
        if (!$this->assignment->time_limit) {
            return null;
        }

        $timeLimit = $this->assignment->time_limit; // in minutes
        $timeElapsed = Carbon::now()->diffInMinutes($this->started_at);

        return max(0, $timeLimit - $timeElapsed);
    }

    public function calculateScore()
    {
        $totalPoints = $this->assignment->total_points;
        $earnedPoints = $this->answers()->sum('points_earned');

        $this->update([
            'score' => $earnedPoints,
            'total_points' => $totalPoints,
            'percentage' => $totalPoints > 0 ? ($earnedPoints / $totalPoints) * 100 : 0,
        ]);

        return $this->percentage;
    }
}
