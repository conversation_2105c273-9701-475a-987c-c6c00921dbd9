<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Assignment extends Model
{
    use HasFactory, HasUuids;

    protected $guarded = ['id'];

    protected $casts = [
        'assignment_files' => 'array',
        'due_date' => 'datetime',
        'is_active' => 'boolean',
        'show_results_immediately' => 'boolean',
    ];

    public function education()
    {
        return $this->belongsTo(Education::class);
    }

    public function questions()
    {
        return $this->hasMany(AssignmentQuestion::class)->orderBy('order');
    }

    public function submissions()
    {
        return $this->hasMany(AssignmentSubmission::class);
    }

    public function userSubmission($userId)
    {
        return $this->submissions()->where('user_id', $userId)->first();
    }

    public function isOverdue()
    {
        return $this->due_date && Carbon::now()->isAfter($this->due_date);
    }

    public function canUserSubmit($userId)
    {
        if (!$this->is_active || $this->isOverdue()) {
            return false;
        }

        $submissionCount = $this->submissions()
            ->where('user_id', $userId)
            ->where('status', 'submitted')
            ->count();

        return $submissionCount < $this->max_attempts;
    }

    public function getTotalPointsAttribute()
    {
        return $this->questions()->sum('points');
    }
}
