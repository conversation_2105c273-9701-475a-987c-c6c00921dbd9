<?php

namespace App\Filament\Resources\AssignmentResource\Pages;

use App\Filament\Resources\AssignmentResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAssignment extends EditRecord
{
    protected static string $resource = AssignmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
            Actions\Action::make('manage_questions')
                ->label('Manage Questions')
                ->icon('heroicon-o-question-mark-circle')
                ->url(fn () => route('filament.app.resources.assignment-questions.index', [
                    'tableFilters' => [
                        'assignment' => [
                            'value' => $this->record->id
                        ]
                    ]
                ]))
                ->color('success')
                ->openUrlInNewTab(),

            Actions\Action::make('add_question')
                ->label('Add Question')
                ->icon('heroicon-o-plus-circle')
                ->url(fn () => route('filament.app.resources.assignment-questions.create', [
                    'assignment_id' => $this->record->id
                ]))
                ->color('primary')
                ->openUrlInNewTab(),
        ];
    }
}
