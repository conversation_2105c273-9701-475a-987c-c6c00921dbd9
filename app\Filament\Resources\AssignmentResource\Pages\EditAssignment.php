<?php

namespace App\Filament\Resources\AssignmentResource\Pages;

use App\Filament\Resources\AssignmentResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAssignment extends EditRecord
{
    protected static string $resource = AssignmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
            Actions\Action::make('manage_questions')
                ->label('Manage Questions')
                ->icon('heroicon-o-question-mark-circle')
                ->url(fn () => route('filament.admin.resources.assignment-questions.index', ['assignment' => $this->record->id]))
                ->color('success'),
        ];
    }
}
