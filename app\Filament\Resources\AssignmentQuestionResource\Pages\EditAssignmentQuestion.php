<?php

namespace App\Filament\Resources\AssignmentQuestionResource\Pages;

use App\Filament\Resources\AssignmentQuestionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAssignmentQuestion extends EditRecord
{
    protected static string $resource = AssignmentQuestionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }
}
