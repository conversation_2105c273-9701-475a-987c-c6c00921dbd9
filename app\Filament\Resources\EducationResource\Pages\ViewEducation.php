<?php

namespace App\Filament\Resources\EducationResource\Pages;

use App\Filament\Resources\EducationResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewEducation extends ViewRecord
{
    protected static string $resource = EducationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // No actions for read-only view
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Education Material Details')
                    ->schema([
                        Infolists\Components\TextEntry::make('domain.name')
                            ->label('Domain')
                            ->badge()
                            ->color('primary'),
                        Infolists\Components\TextEntry::make('title')
                            ->label('Title')
                            ->size(Infolists\Components\TextEntry\TextEntrySize::Large)
                            ->weight('bold'),
                        Infolists\Components\TextEntry::make('description')
                            ->label('Description')
                            ->columnSpanFull()
                            ->prose(),
                    ])
                    ->columns(2)
                    ->collapsible(),

                // YouTube Video Section - Most prominent
                Infolists\Components\Section::make('Video Content')
                    ->description('Watch the educational video below')
                    ->schema([
                        Infolists\Components\ViewEntry::make('youtube_video')
                            ->label('')
                            ->view('filament.infolists.youtube-video')
                            ->columnSpanFull(),
                    ])
                    ->columns(1)
                    ->compact(),

                // Learning File Section
                Infolists\Components\Section::make('Additional Resources')
                    ->description('Download additional learning materials')
                    ->schema([
                        Infolists\Components\ViewEntry::make('learning_file')
                            ->label('Learning File')
                            ->view('filament.infolists.learning-file')
                            ->columnSpanFull(),
                    ])
                    ->columns(1)
                    ->collapsible(),
            ]);
    }
}
