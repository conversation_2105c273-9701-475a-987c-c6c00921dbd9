<?php

namespace App\Filament\Resources\EducationResource\Pages;

use App\Filament\Resources\EducationResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Support\HtmlString;

class ViewEducation extends ViewRecord
{
    protected static string $resource = EducationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // No actions for read-only view
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Education Material Details')
                    ->schema([
                        Infolists\Components\TextEntry::make('domain.name')
                            ->label('Domain'),
                        Infolists\Components\TextEntry::make('title')
                            ->label('Title'),
                        Infolists\Components\TextEntry::make('description')
                            ->label('Description')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
                
                Infolists\Components\Section::make('Learning Resources')
                    ->schema([
                        Infolists\Components\TextEntry::make('youtube_video')
                            ->label('YouTube Video')
                            ->formatStateUsing(function ($record) {
                                if (!$record->youtube_url) {
                                    return 'No video available';
                                }
                                
                                $embedUrl = $record->youtube_embed_url;
                                if (!$embedUrl) {
                                    return 'Invalid YouTube URL';
                                }
                                
                                return new HtmlString('
                                    <div class="aspect-video w-full max-w-2xl">
                                        <iframe 
                                            class="w-full h-full rounded-lg" 
                                            src="' . $embedUrl . '" 
                                            title="YouTube video player" 
                                            frameborder="0" 
                                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" 
                                            allowfullscreen>
                                        </iframe>
                                    </div>
                                ');
                            })
                            ->columnSpanFull(),
                            
                        Infolists\Components\TextEntry::make('learning_file')
                            ->label('Learning File')
                            ->formatStateUsing(function ($record) {
                                if (!$record->learning_file) {
                                    return 'No file available';
                                }
                                
                                $fileName = basename($record->learning_file);
                                $fileUrl = asset('storage/' . $record->learning_file);
                                
                                return new HtmlString('
                                    <div class="flex items-center space-x-2">
                                        <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        <button 
                                            type="button"
                                            onclick="openFileModal(\'' . $fileUrl . '\', \'' . $fileName . '\')"
                                            class="text-blue-600 hover:text-blue-800 underline"
                                        >
                                            ' . $fileName . '
                                        </button>
                                    </div>
                                    
                                    <!-- Modal -->
                                    <div id="fileModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
                                        <div class="flex items-center justify-center min-h-screen p-4">
                                            <div class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
                                                <div class="flex justify-between items-center p-4 border-b">
                                                    <h3 class="text-lg font-medium" id="modalTitle">Learning File</h3>
                                                    <button onclick="closeFileModal()" class="text-gray-400 hover:text-gray-600">
                                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                        </svg>
                                                    </button>
                                                </div>
                                                <div class="p-4 h-96 overflow-auto">
                                                    <iframe id="modalFrame" class="w-full h-full" frameborder="0"></iframe>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <script>
                                        function openFileModal(url, title) {
                                            document.getElementById("modalTitle").textContent = title;
                                            document.getElementById("modalFrame").src = url;
                                            document.getElementById("fileModal").classList.remove("hidden");
                                        }
                                        
                                        function closeFileModal() {
                                            document.getElementById("fileModal").classList.add("hidden");
                                            document.getElementById("modalFrame").src = "";
                                        }
                                        
                                        // Close modal when clicking outside
                                        document.getElementById("fileModal").addEventListener("click", function(e) {
                                            if (e.target === this) {
                                                closeFileModal();
                                            }
                                        });
                                    </script>
                                ');
                            })
                            ->columnSpanFull(),
                    ])
                    ->columns(1),
            ]);
    }
}
