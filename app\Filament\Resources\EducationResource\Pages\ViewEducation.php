<?php

namespace App\Filament\Resources\EducationResource\Pages;

use App\Filament\Resources\EducationResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewEducation extends ViewRecord
{
    protected static string $resource = EducationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // No actions for read-only view
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Education Material Details')
                    ->schema([
                        Infolists\Components\TextEntry::make('domain.name')
                            ->label('Domain'),
                        Infolists\Components\TextEntry::make('title')
                            ->label('Title'),
                        Infolists\Components\TextEntry::make('description')
                            ->label('Description')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Learning Resources')
                    ->schema([
                        Infolists\Components\ViewEntry::make('youtube_video')
                            ->label('YouTube Video')
                            ->view('filament.infolists.youtube-video')
                            ->columnSpanFull(),

                        Infolists\Components\ViewEntry::make('learning_file')
                            ->label('Learning File')
                            ->view('filament.infolists.learning-file')
                            ->columnSpanFull(),
                    ])
                    ->columns(1),
            ]);
    }
}
