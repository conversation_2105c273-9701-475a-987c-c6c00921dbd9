<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AssignmentQuestionResource\Pages;
use App\Models\AssignmentQuestion;
use App\Models\Assignment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class AssignmentQuestionResource extends Resource
{
    protected static ?string $model = AssignmentQuestion::class;

    protected static ?string $navigationIcon = 'heroicon-o-question-mark-circle';

    protected static ?string $navigationLabel = 'Assignment Questions';

    protected static ?string $modelLabel = 'Question';

    protected static ?string $pluralModelLabel = 'Assignment Questions';

    protected static ?string $navigationGroup = 'Administration';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Question Details')
                    ->schema([
                        Forms\Components\Select::make('assignment_id')
                            ->label('Assignment')
                            ->options(Assignment::with('education')->get()->mapWithKeys(function ($assignment) {
                                return [$assignment->id => $assignment->education->title . ' - ' . $assignment->title];
                            }))
                            ->required()
                            ->searchable()
                            ->preload(),

                        Forms\Components\Select::make('type')
                            ->label('Question Type')
                            ->options([
                                'multiple_choice' => 'Multiple Choice',
                                'true_false' => 'True/False',
                            ])
                            ->default('multiple_choice')
                            ->required()
                            ->live(),

                        Forms\Components\TextInput::make('points')
                            ->label('Points')
                            ->numeric()
                            ->minValue(1)
                            ->maxValue(100)
                            ->default(1)
                            ->required(),

                        Forms\Components\TextInput::make('order')
                            ->label('Order')
                            ->numeric()
                            ->minValue(0)
                            ->default(0)
                            ->helperText('Question order in the assignment'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Question Content')
                    ->schema([
                        Forms\Components\Textarea::make('question')
                            ->label('Question Text')
                            ->required()
                            ->rows(3)
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Answer Options')
                    ->schema([
                        Forms\Components\Repeater::make('options')
                            ->relationship('options')
                            ->schema([
                                Forms\Components\Textarea::make('option_text')
                                    ->label('Option Text')
                                    ->required()
                                    ->rows(2)
                                    ->columnSpanFull(),
                                Forms\Components\Toggle::make('is_correct')
                                    ->label('Correct Answer')
                                    ->helperText('Mark this option as the correct answer'),
                                Forms\Components\TextInput::make('order')
                                    ->label('Order')
                                    ->numeric()
                                    ->default(0),
                            ])
                            ->columns(2)
                            ->minItems(fn (Forms\Get $get) => $get('type') === 'true_false' ? 2 : 2)
                            ->maxItems(fn (Forms\Get $get) => $get('type') === 'true_false' ? 2 : 6)
                            ->defaultItems(fn (Forms\Get $get) => $get('type') === 'true_false' ? 2 : 4)
                            ->addActionLabel('Add Option')
                            ->reorderable()
                            ->collapsible()
                            ->itemLabel(fn (array $state): ?string => $state['option_text'] ?? null),
                    ])
                    ->visible(fn (Forms\Get $get) => $get('type') !== null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('assignment.title')
                    ->label('Assignment')
                    ->searchable()
                    ->sortable()
                    ->limit(30),

                Tables\Columns\TextColumn::make('question')
                    ->label('Question')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),

                Tables\Columns\TextColumn::make('type')
                    ->label('Type')
                    ->badge()
                    ->color(fn ($state) => match($state) {
                        'multiple_choice' => 'primary',
                        'true_false' => 'success',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn ($state) => match($state) {
                        'multiple_choice' => 'Multiple Choice',
                        'true_false' => 'True/False',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('points')
                    ->label('Points')
                    ->sortable(),

                Tables\Columns\TextColumn::make('order')
                    ->label('Order')
                    ->sortable(),

                Tables\Columns\TextColumn::make('options_count')
                    ->label('Options')
                    ->counts('options'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('assignment')
                    ->relationship('assignment', 'title'),
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'multiple_choice' => 'Multiple Choice',
                        'true_false' => 'True/False',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('assignment_id', 'asc')
            ->defaultSort('order', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAssignmentQuestions::route('/'),
            'create' => Pages\CreateAssignmentQuestion::route('/create'),
            'view' => Pages\ViewAssignmentQuestion::route('/{record}'),
            'edit' => Pages\EditAssignmentQuestion::route('/{record}/edit'),
        ];
    }
}
