<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AssignmentQuestionResource\Pages;
use App\Models\AssignmentQuestion;
use App\Models\Assignment;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Str;

class AssignmentQuestionResource extends Resource
{
    protected static ?string $model = AssignmentQuestion::class;

    protected static ?string $navigationIcon = 'heroicon-o-question-mark-circle';

    protected static ?string $navigationLabel = 'Assignment Questions';

    protected static ?string $modelLabel = 'Question';

    protected static ?string $pluralModelLabel = 'Assignment Questions';

    protected static ?string $navigationGroup = 'Administration';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Question Details')
                    ->schema([
                        Forms\Components\Select::make('assignment_id')
                            ->label('Assignment')
                            ->options(Assignment::with('education')->get()->mapWithKeys(function ($assignment) {
                                return [$assignment->id => $assignment->education->title . ' - ' . $assignment->title];
                            }))
                            ->required()
                            ->searchable()
                            ->preload(),

                        Forms\Components\Select::make('type')
                            ->label('Question Type')
                            ->options([
                                'multiple_choice' => 'Multiple Choice',
                                'true_false' => 'True/False',
                            ])
                            ->default('multiple_choice')
                            ->required()
                            ->live(),

                        Forms\Components\TextInput::make('points')
                            ->label('Points')
                            ->numeric()
                            ->minValue(1)
                            ->maxValue(100)
                            ->default(1)
                            ->required(),

                        Forms\Components\TextInput::make('order')
                            ->label('Order')
                            ->numeric()
                            ->minValue(0)
                            ->default(0)
                            ->helperText('Question order in the assignment'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Question Content')
                    ->schema([
                        Forms\Components\Textarea::make('question')
                            ->label('Question Text')
                            ->required()
                            ->rows(3)
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Answer Options')
                    ->description(fn (Forms\Get $get) => match($get('type')) {
                        'multiple_choice' => 'Add 2-6 answer options. Mark exactly one as correct.',
                        'true_false' => 'True/False options will be automatically created.',
                        default => 'Select a question type first.'
                    })
                    ->schema([
                        Forms\Components\Repeater::make('options')
                            ->relationship('options')
                            ->schema([
                                Forms\Components\Grid::make(12)
                                    ->schema([
                                        Forms\Components\TextInput::make('order')
                                            ->label('Order')
                                            ->numeric()
                                            ->default(1)
                                            ->minValue(1)
                                            ->maxValue(10)
                                            ->columnSpan(2)
                                            ->helperText('Display order'),

                                        Forms\Components\Textarea::make('option_text')
                                            ->label('Option Text')
                                            ->required()
                                            ->rows(2)
                                            ->columnSpan(8)
                                            ->placeholder('Enter the answer option text...'),

                                        Forms\Components\Toggle::make('is_correct')
                                            ->label('Correct')
                                            ->columnSpan(2)
                                            ->helperText('Mark as correct answer')
                                            ->live()
                                            ->helperText('Only one option can be correct for multiple choice questions'),
                                    ]),

                                Forms\Components\Placeholder::make('option_preview')
                                    ->label('')
                                    ->content(fn (Forms\Get $get) => $get('option_text') ?
                                        "Preview: " . Str::limit($get('option_text'), 100) :
                                        'Enter option text to see preview'
                                    )
                                    ->visible(fn (Forms\Get $get) => !empty($get('option_text')))
                                    ->extraAttributes(['class' => 'text-sm text-gray-600 italic']),
                            ])
                            ->columns(1)
                            ->minItems(fn (Forms\Get $get) => match($get('type')) {
                                'true_false' => 2,
                                'multiple_choice' => 2,
                                default => 0
                            })
                            ->maxItems(fn (Forms\Get $get) => match($get('type')) {
                                'true_false' => 2,
                                'multiple_choice' => 6,
                                default => 6
                            })
                            ->defaultItems(2)
                            ->addActionLabel(fn (Forms\Get $get) => match($get('type')) {
                                'multiple_choice' => 'Add Answer Option',
                                'true_false' => 'Add Option',
                                default => 'Add Option'
                            })
                            ->reorderable('order')
                            ->reorderableWithButtons()
                            ->collapsible()
                            ->cloneable()
                            ->itemLabel(fn (array $state): ?string =>
                                !empty($state['option_text']) ?
                                    (($state['is_correct'] ?? false) ? '✓ ' : '') . Str::limit($state['option_text'], 50) :
                                    'New Option'
                            )
                            ->deleteAction(
                                fn (Forms\Components\Actions\Action $action) => $action
                                    ->requiresConfirmation()
                                    ->modalHeading('Delete Option')
                                    ->modalDescription('Are you sure you want to delete this answer option?')
                            )
                            ->visible(fn (Forms\Get $get) => $get('type') !== null),

                        // Help text for multiple choice
                        Forms\Components\Placeholder::make('multiple_choice_help')
                            ->label('')
                            ->content('💡 Tip: Use the "Add Answer Option" button above to add options. Mark exactly one option as correct.')
                            ->visible(fn (Forms\Get $get) => $get('type') === 'multiple_choice'),
                    ])
                    ->visible(fn (Forms\Get $get) => $get('type') !== null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('assignment.title')
                    ->label('Assignment')
                    ->searchable()
                    ->sortable()
                    ->limit(30),

                Tables\Columns\TextColumn::make('question')
                    ->label('Question')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 50) {
                            return null;
                        }
                        return $state;
                    }),

                Tables\Columns\TextColumn::make('type')
                    ->label('Type')
                    ->badge()
                    ->color(fn ($state) => match($state) {
                        'multiple_choice' => 'primary',
                        'true_false' => 'success',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn ($state) => match($state) {
                        'multiple_choice' => 'Multiple Choice',
                        'true_false' => 'True/False',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('points')
                    ->label('Points')
                    ->sortable(),

                Tables\Columns\TextColumn::make('order')
                    ->label('Order')
                    ->sortable(),

                Tables\Columns\TextColumn::make('options_count')
                    ->label('Options')
                    ->counts('options')
                    ->badge()
                    ->color(fn ($record) => match($record->type) {
                        'multiple_choice' => $record->options_count >= 2 && $record->options_count <= 6 ? 'success' : 'danger',
                        'true_false' => $record->options_count === 2 ? 'success' : 'danger',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('correct_answer')
                    ->label('Correct Answer')
                    ->state(fn ($record) => $record->correct_answer_text ?? 'Not set')
                    ->limit(30)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 30) {
                            return null;
                        }
                        return $state;
                    })
                    ->color(fn ($record) => $record->correct_answer_text ? 'success' : 'danger'),

                Tables\Columns\IconColumn::make('is_valid')
                    ->label('Valid')
                    ->state(fn ($record) => $record->isValid())
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger')
                    ->tooltip(fn ($record) => $record->isValid() ? 'Question is valid' : implode(' ', $record->getValidationErrors())),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('assignment')
                    ->relationship('assignment', 'title'),
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'multiple_choice' => 'Multiple Choice',
                        'true_false' => 'True/False',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('assignment_id', 'asc')
            ->defaultSort('order', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAssignmentQuestions::route('/'),
            'create' => Pages\CreateAssignmentQuestion::route('/create'),
            'view' => Pages\ViewAssignmentQuestion::route('/{record}'),
            'edit' => Pages\EditAssignmentQuestion::route('/{record}/edit'),
        ];
    }
}
