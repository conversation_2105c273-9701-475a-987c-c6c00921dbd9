@if ($getRecord()->learning_file)
    @php
        $fileName = basename($getRecord()->learning_file);
        $fileUrl = asset('storage/' . $getRecord()->learning_file);
    @endphp

    <div class="flex items-center space-x-2">
        <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
            </path>
        </svg>
        <button type="button" onclick="openFileModal('{{ $fileUrl }}', '{{ $fileName }}')"
            class="text-blue-600 hover:text-blue-800 underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded">
            {{ $fileName }}
        </button>
    </div>

    <!-- Modal -->
    <div id="fileModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-xl">
                <div class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100" id="modalTitle">Learning File</h3>
                    <button onclick="closeFileModal()"
                        class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-4 h-96 overflow-auto">
                    <iframe id="modalFrame" class="w-full h-full border-0 rounded" frameborder="0"></iframe>
                </div>
                <div class="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-2">
                    <a href="{{ $fileUrl }}" target="_blank"
                        class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                        Download
                    </a>
                    <button onclick="closeFileModal()"
                        class="inline-flex items-center px-4 py-2 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            function openFileModal(url, title) {
                document.getElementById("modalTitle").textContent = title;
                document.getElementById("modalFrame").src = url;
                document.getElementById("fileModal").classList.remove("hidden");
                document.body.style.overflow = 'hidden'; // Prevent background scrolling
            }

            function closeFileModal() {
                document.getElementById("fileModal").classList.add("hidden");
                document.getElementById("modalFrame").src = "";
                document.body.style.overflow = 'auto'; // Restore scrolling
            }

            // Close modal when clicking outside
            document.addEventListener('DOMContentLoaded', function() {
                const modal = document.getElementById("fileModal");
                if (modal) {
                    modal.addEventListener("click", function(e) {
                        if (e.target === this) {
                            closeFileModal();
                        }
                    });

                    // Close modal with Escape key
                    document.addEventListener('keydown', function(e) {
                        if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                            closeFileModal();
                        }
                    });
                }
            });
        </script>
    @endpush
@else
    <div class="text-gray-500 italic">
        No file available
    </div>
@endif
