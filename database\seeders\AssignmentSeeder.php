<?php

namespace Database\Seeders;

use App\Models\Assignment;
use App\Models\AssignmentQuestion;
use App\Models\AssignmentQuestionOption;
use App\Models\Education;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class AssignmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $educations = Education::all();

        if ($educations->isEmpty()) {
            $this->command->info('No education records found. Please run EducationSeeder first.');
            return;
        }

        foreach ($educations->take(2) as $index => $education) {
            $assignment = Assignment::create([
                'education_id' => $education->id,
                'title' => 'Quiz: ' . $education->title,
                'description' => 'Test your understanding of ' . $education->title,
                'guide' => '<p>Please read the instructions carefully:</p>
                           <ul>
                               <li>This quiz contains multiple choice questions</li>
                               <li>You have a limited time to complete it</li>
                               <li>Choose the best answer for each question</li>
                               <li>You can only attempt this quiz once</li>
                           </ul>
                           <p><strong>Good luck!</strong></p>',
                'time_limit' => 30, // 30 minutes
                'due_date' => Carbon::now()->addDays(7),
                'is_active' => true,
                'max_attempts' => 1,
                'show_results_immediately' => true,
            ]);

            // Create sample questions based on education content
            $this->createSampleQuestions($assignment, $index);
        }
    }

    private function createSampleQuestions(Assignment $assignment, int $index)
    {
        $questionSets = [
            // Question set 1 - One Data Indonesia
            [
                [
                    'question' => 'What is the main principle of One Data Indonesia?',
                    'type' => 'multiple_choice',
                    'points' => 10,
                    'order' => 1,
                    'options' => [
                        ['option_text' => 'Data integration and standardization', 'is_correct' => true, 'order' => 1],
                        ['option_text' => 'Data collection only', 'is_correct' => false, 'order' => 2],
                        ['option_text' => 'Data storage optimization', 'is_correct' => false, 'order' => 3],
                        ['option_text' => 'Data visualization', 'is_correct' => false, 'order' => 4],
                    ]
                ],
                [
                    'question' => 'Statistical data quality is important for decision making.',
                    'type' => 'true_false',
                    'points' => 5,
                    'order' => 2,
                    'options' => [
                        ['option_text' => 'True', 'is_correct' => true, 'order' => 1],
                        ['option_text' => 'False', 'is_correct' => false, 'order' => 2],
                    ]
                ],
                [
                    'question' => 'Which of the following is a key component of metadata?',
                    'type' => 'multiple_choice',
                    'points' => 10,
                    'order' => 3,
                    'options' => [
                        ['option_text' => 'Data description and context', 'is_correct' => true, 'order' => 1],
                        ['option_text' => 'Raw data values', 'is_correct' => false, 'order' => 2],
                        ['option_text' => 'Database passwords', 'is_correct' => false, 'order' => 3],
                        ['option_text' => 'Server configurations', 'is_correct' => false, 'order' => 4],
                    ]
                ]
            ],
            // Question set 2 - Data Quality Management
            [
                [
                    'question' => 'What are the main dimensions of data quality?',
                    'type' => 'multiple_choice',
                    'points' => 10,
                    'order' => 1,
                    'options' => [
                        ['option_text' => 'Accuracy, completeness, consistency, timeliness', 'is_correct' => true, 'order' => 1],
                        ['option_text' => 'Speed, size, storage, security', 'is_correct' => false, 'order' => 2],
                        ['option_text' => 'Cost, complexity, compatibility, control', 'is_correct' => false, 'order' => 3],
                        ['option_text' => 'Input, process, output, feedback', 'is_correct' => false, 'order' => 4],
                    ]
                ],
                [
                    'question' => 'Data validation should be performed only at the end of data collection.',
                    'type' => 'true_false',
                    'points' => 5,
                    'order' => 2,
                    'options' => [
                        ['option_text' => 'True', 'is_correct' => false, 'order' => 1],
                        ['option_text' => 'False', 'is_correct' => true, 'order' => 2],
                    ]
                ],
                [
                    'question' => 'Which process helps ensure data consistency across different systems?',
                    'type' => 'multiple_choice',
                    'points' => 10,
                    'order' => 3,
                    'options' => [
                        ['option_text' => 'Data standardization', 'is_correct' => true, 'order' => 1],
                        ['option_text' => 'Data compression', 'is_correct' => false, 'order' => 2],
                        ['option_text' => 'Data encryption', 'is_correct' => false, 'order' => 3],
                        ['option_text' => 'Data backup', 'is_correct' => false, 'order' => 4],
                    ]
                ]
            ]
        ];

        $questions = $questionSets[$index] ?? $questionSets[0];

        foreach ($questions as $questionData) {
            $question = AssignmentQuestion::create([
                'assignment_id' => $assignment->id,
                'question' => $questionData['question'],
                'type' => $questionData['type'],
                'points' => $questionData['points'],
                'order' => $questionData['order'],
            ]);

            foreach ($questionData['options'] as $optionData) {
                AssignmentQuestionOption::create([
                    'question_id' => $question->id,
                    'option_text' => $optionData['option_text'],
                    'is_correct' => $optionData['is_correct'],
                    'order' => $optionData['order'],
                ]);
            }
        }
    }
}
