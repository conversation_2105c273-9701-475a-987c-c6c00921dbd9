<?php

namespace Database\Seeders;

use App\Models\Assignment;
use App\Models\AssignmentQuestion;
use App\Models\AssignmentQuestionOption;
use App\Models\Education;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class AssignmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $educations = Education::all();

        if ($educations->isEmpty()) {
            $this->command->info('No education records found. Please run EducationSeeder first.');
            return;
        }

        foreach ($educations->take(2) as $index => $education) {
            $assignment = Assignment::create([
                'education_id' => $education->id,
                'title' => 'Quiz: ' . $education->title,
                'description' => 'Test your understanding of ' . $education->title,
                'guide' => '<p>Please read the instructions carefully:</p>
                           <ul>
                               <li>This quiz contains multiple choice questions</li>
                               <li>You have a limited time to complete it</li>
                               <li>Choose the best answer for each question</li>
                               <li>You can only attempt this quiz once</li>
                           </ul>
                           <p><strong>Good luck!</strong></p>',
                'time_limit' => 30, // 30 minutes
                'due_date' => Carbon::now()->addDays(7),
                'is_active' => true,
                'max_attempts' => 1,
                'show_results_immediately' => true,
            ]);

            // Create sample questions based on education content
            $this->createSampleQuestions($assignment, $index);
        }
    }

    private function createSampleQuestions(Assignment $assignment, int $index)
    {
        $questionSets = [
            // Question set 1 - One Data Indonesia
            [
                [
                    'question' => 'What is the main principle of One Data Indonesia?',
                    'type' => 'multiple_choice',
                    'points' => 10,
                    'order' => 1,
                    'options' => [
                        ['option_text' => 'Data integration and standardization across government institutions', 'is_correct' => true, 'order' => 1],
                        ['option_text' => 'Data collection only from primary sources', 'is_correct' => false, 'order' => 2],
                        ['option_text' => 'Data storage optimization for cost reduction', 'is_correct' => false, 'order' => 3],
                        ['option_text' => 'Data visualization for public consumption', 'is_correct' => false, 'order' => 4],
                        ['option_text' => 'Data security and privacy protection only', 'is_correct' => false, 'order' => 5],
                    ]
                ],
                [
                    'question' => 'Statistical data quality is important for decision making.',
                    'type' => 'true_false',
                    'points' => 5,
                    'order' => 2,
                    'options' => [
                        ['option_text' => 'True', 'is_correct' => true, 'order' => 1],
                        ['option_text' => 'False', 'is_correct' => false, 'order' => 2],
                    ]
                ],
                [
                    'question' => 'Which of the following is a key component of metadata in statistical data management?',
                    'type' => 'multiple_choice',
                    'points' => 10,
                    'order' => 3,
                    'options' => [
                        ['option_text' => 'Data description, context, and methodology information', 'is_correct' => true, 'order' => 1],
                        ['option_text' => 'Raw data values and numerical calculations', 'is_correct' => false, 'order' => 2],
                        ['option_text' => 'Database passwords and security credentials', 'is_correct' => false, 'order' => 3],
                        ['option_text' => 'Server configurations and hardware specifications', 'is_correct' => false, 'order' => 4],
                        ['option_text' => 'User interface design and color schemes', 'is_correct' => false, 'order' => 5],
                    ]
                ],
                [
                    'question' => 'In the context of One Data Indonesia, which stakeholders are primarily responsible for data governance?',
                    'type' => 'multiple_choice',
                    'points' => 15,
                    'order' => 4,
                    'options' => [
                        ['option_text' => 'Government institutions, statistical agencies, and data producers', 'is_correct' => true, 'order' => 1],
                        ['option_text' => 'Private companies and commercial data vendors only', 'is_correct' => false, 'order' => 2],
                        ['option_text' => 'International organizations and foreign governments', 'is_correct' => false, 'order' => 3],
                        ['option_text' => 'Individual citizens and social media platforms', 'is_correct' => false, 'order' => 4],
                    ]
                ]
            ],
            // Question set 2 - Data Quality Management
            [
                [
                    'question' => 'What are the main dimensions of data quality in statistical data management?',
                    'type' => 'multiple_choice',
                    'points' => 10,
                    'order' => 1,
                    'options' => [
                        ['option_text' => 'Accuracy, completeness, consistency, timeliness, and relevance', 'is_correct' => true, 'order' => 1],
                        ['option_text' => 'Speed, size, storage capacity, and security protocols', 'is_correct' => false, 'order' => 2],
                        ['option_text' => 'Cost efficiency, complexity reduction, and compatibility', 'is_correct' => false, 'order' => 3],
                        ['option_text' => 'Input methods, processing speed, and output formats', 'is_correct' => false, 'order' => 4],
                        ['option_text' => 'Database design, user interface, and reporting tools', 'is_correct' => false, 'order' => 5],
                    ]
                ],
                [
                    'question' => 'Data validation should be performed only at the end of data collection.',
                    'type' => 'true_false',
                    'points' => 5,
                    'order' => 2,
                    'options' => [
                        ['option_text' => 'True', 'is_correct' => false, 'order' => 1],
                        ['option_text' => 'False', 'is_correct' => true, 'order' => 2],
                    ]
                ],
                [
                    'question' => 'Which process helps ensure data consistency across different systems and organizations?',
                    'type' => 'multiple_choice',
                    'points' => 10,
                    'order' => 3,
                    'options' => [
                        ['option_text' => 'Data standardization and harmonization procedures', 'is_correct' => true, 'order' => 1],
                        ['option_text' => 'Data compression and storage optimization', 'is_correct' => false, 'order' => 2],
                        ['option_text' => 'Data encryption and security protocols', 'is_correct' => false, 'order' => 3],
                        ['option_text' => 'Data backup and disaster recovery plans', 'is_correct' => false, 'order' => 4],
                        ['option_text' => 'Data visualization and reporting tools', 'is_correct' => false, 'order' => 5],
                    ]
                ],
                [
                    'question' => 'In statistical data quality assessment, what is the primary purpose of data profiling?',
                    'type' => 'multiple_choice',
                    'points' => 15,
                    'order' => 4,
                    'options' => [
                        ['option_text' => 'To analyze data structure, content, and quality issues systematically', 'is_correct' => true, 'order' => 1],
                        ['option_text' => 'To create user profiles for data access control', 'is_correct' => false, 'order' => 2],
                        ['option_text' => 'To generate statistical reports for management', 'is_correct' => false, 'order' => 3],
                        ['option_text' => 'To design database schemas and relationships', 'is_correct' => false, 'order' => 4],
                    ]
                ]
            ]
        ];

        $questions = $questionSets[$index] ?? $questionSets[0];

        foreach ($questions as $questionData) {
            $question = AssignmentQuestion::create([
                'assignment_id' => $assignment->id,
                'question' => $questionData['question'],
                'type' => $questionData['type'],
                'points' => $questionData['points'],
                'order' => $questionData['order'],
            ]);

            foreach ($questionData['options'] as $optionData) {
                AssignmentQuestionOption::create([
                    'question_id' => $question->id,
                    'option_text' => $optionData['option_text'],
                    'is_correct' => $optionData['is_correct'],
                    'order' => $optionData['order'],
                ]);
            }
        }
    }
}
