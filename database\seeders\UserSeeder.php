<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = [
            [
                'name' => 'BPS Kota Pontianak',
                'email' => '<EMAIL>',
                'password' => Hash::make('adminbps6171')
            ],
            [
                'name' => '<PERSON>as Komu<PERSON> dan Informatika Kota Pontianak',
                'email' => '<EMAIL>',
                'password' => Hash::make('dki123456')
            ],
            [
                'name' => 'Dinas Kependudukan dan Catatan Sipil Kota Pontianak',
                'email' => '<EMAIL>',
                'password' => Hash::make('disdukcapil123456')
            ],
        ];

        foreach ($users as $user) {
            User::create([
                'name' => $user['name'],
                'email' => $user['email'],
                'password' => $user['password'],
            ]);
        }
    }
}
