<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assignment_answers', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('submission_id')->references('id')->on('assignment_submissions')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->foreignUuid('question_id')->references('id')->on('assignment_questions')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->foreignUuid('selected_option_id')->nullable()->references('id')->on('assignment_question_options')
                ->onUpdate('cascade')
                ->onDelete('set null');
            $table->boolean('is_correct')->nullable();
            $table->integer('points_earned')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assignment_answers');
    }
};
