<?php

namespace App\Filament\Resources\AssignmentQuestionResource\Pages;

use App\Filament\Resources\AssignmentQuestionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewAssignmentQuestion extends ViewRecord
{
    protected static string $resource = AssignmentQuestionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Question Details')
                    ->schema([
                        Infolists\Components\TextEntry::make('assignment.title')
                            ->label('Assignment'),
                        Infolists\Components\TextEntry::make('type')
                            ->label('Question Type')
                            ->formatStateUsing(fn ($state) => match($state) {
                                'multiple_choice' => 'Multiple Choice',
                                'true_false' => 'True/False',
                                default => $state,
                            }),
                        Infolists\Components\TextEntry::make('points')
                            ->label('Points'),
                        Infolists\Components\TextEntry::make('order')
                            ->label('Order'),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Question Content')
                    ->schema([
                        Infolists\Components\TextEntry::make('question')
                            ->label('Question Text')
                            ->columnSpanFull(),
                    ]),

                Infolists\Components\Section::make('Answer Options')
                    ->schema([
                        Infolists\Components\RepeatableEntry::make('options')
                            ->schema([
                                Infolists\Components\TextEntry::make('option_text')
                                    ->label('Option'),
                                Infolists\Components\IconEntry::make('is_correct')
                                    ->label('Correct')
                                    ->boolean()
                                    ->trueIcon('heroicon-o-check-circle')
                                    ->falseIcon('heroicon-o-x-circle')
                                    ->trueColor('success')
                                    ->falseColor('gray'),
                                Infolists\Components\TextEntry::make('order')
                                    ->label('Order'),
                            ])
                            ->columns(3),
                    ]),
            ]);
    }
}
