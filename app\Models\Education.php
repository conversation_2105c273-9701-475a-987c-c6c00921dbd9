<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Education extends Model
{
    use HasFactory, HasUuids;

    protected $table = 'educations';

    protected $guarded = ['id'];

    public function domain()
    {
        return $this->belongsTo(Domain::class);
    }

    public function assignments()
    {
        return $this->hasMany(Assignment::class);
    }

    public function getYoutubeEmbedUrlAttribute()
    {
        if (!$this->youtube_url) {
            return null;
        }

        // Convert YouTube URL to embed URL
        $url = $this->youtube_url;

        // Handle different YouTube URL formats
        if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/', $url, $matches)) {
            return 'https://www.youtube.com/embed/' . $matches[1];
        }

        return $url;
    }
}
