<?php

namespace App\Filament\Resources\AssignmentQuestionResource\Pages;

use App\Filament\Resources\AssignmentQuestionResource;
use Filament\Resources\Pages\CreateRecord;

class CreateAssignmentQuestion extends CreateRecord
{
    protected static string $resource = AssignmentQuestionResource::class;

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Pre-select assignment if passed as parameter
        if (request()->has('assignment_id')) {
            $data['assignment_id'] = request()->get('assignment_id');
        }

        return $data;
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Auto-populate True/False options if type is true_false and no options exist
        if ($data['type'] === 'true_false' && empty($data['options'])) {
            $data['options'] = [
                [
                    'option_text' => 'True',
                    'is_correct' => false,
                    'order' => 1,
                ],
                [
                    'option_text' => 'False',
                    'is_correct' => false,
                    'order' => 2,
                ],
            ];
        }

        // Ensure options are ordered correctly if they exist
        if (!empty($data['options'])) {
            // Auto-assign order if not set
            foreach ($data['options'] as $index => &$option) {
                if (!isset($option['order']) || $option['order'] === 0) {
                    $option['order'] = $index + 1;
                }
            }

            // Sort by order
            usort($data['options'], function ($a, $b) {
                return ($a['order'] ?? 0) <=> ($b['order'] ?? 0);
            });
        }

        return $data;
    }

    protected function afterCreate(): void
    {
        // Validate that multiple choice questions have exactly one correct answer
        $record = $this->getRecord();
        if ($record->type === 'multiple_choice') {
            $correctCount = $record->options()->where('is_correct', true)->count();
            if ($correctCount === 0) {
                $this->notify('warning', 'Warning: No correct answer selected for this multiple choice question.');
            } elseif ($correctCount > 1) {
                $this->notify('warning', 'Warning: Multiple correct answers selected. Only one should be correct for multiple choice questions.');
            }
        }
    }
}
