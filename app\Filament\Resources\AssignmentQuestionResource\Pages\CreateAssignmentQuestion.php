<?php

namespace App\Filament\Resources\AssignmentQuestionResource\Pages;

use App\Filament\Resources\AssignmentQuestionResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateAssignmentQuestion extends CreateRecord
{
    protected static string $resource = AssignmentQuestionResource::class;
    
    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Auto-populate True/False options if type is true_false
        if ($data['type'] === 'true_false' && empty($data['options'])) {
            $data['options'] = [
                [
                    'option_text' => 'True',
                    'is_correct' => false,
                    'order' => 1,
                ],
                [
                    'option_text' => 'False',
                    'is_correct' => false,
                    'order' => 2,
                ],
            ];
        }
        
        return $data;
    }
}
