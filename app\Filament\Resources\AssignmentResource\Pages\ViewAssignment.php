<?php

namespace App\Filament\Resources\AssignmentResource\Pages;

use App\Filament\Resources\AssignmentResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewAssignment extends ViewRecord
{
    protected static string $resource = AssignmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Assignment Details')
                    ->schema([
                        Infolists\Components\TextEntry::make('education.title')
                            ->label('Education Material'),
                        Infolists\Components\TextEntry::make('title')
                            ->label('Assignment Title'),
                        Infolists\Components\TextEntry::make('description')
                            ->label('Description')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Assignment Guide')
                    ->schema([
                        Infolists\Components\TextEntry::make('guide')
                            ->label('Instructions')
                            ->html()
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),

                Infolists\Components\Section::make('Settings')
                    ->schema([
                        Infolists\Components\TextEntry::make('time_limit')
                            ->label('Time Limit')
                            ->formatStateUsing(fn ($state) => $state ? "{$state} minutes" : 'No time limit'),
                        Infolists\Components\TextEntry::make('due_date')
                            ->label('Due Date')
                            ->dateTime(),
                        Infolists\Components\TextEntry::make('max_attempts')
                            ->label('Maximum Attempts'),
                        Infolists\Components\IconEntry::make('is_active')
                            ->label('Active')
                            ->boolean(),
                        Infolists\Components\IconEntry::make('show_results_immediately')
                            ->label('Show Results Immediately')
                            ->boolean(),
                    ])
                    ->columns(3),

                Infolists\Components\Section::make('Statistics')
                    ->schema([
                        Infolists\Components\TextEntry::make('questions_count')
                            ->label('Total Questions')
                            ->state(fn ($record) => $record->questions()->count()),
                        Infolists\Components\TextEntry::make('submissions_count')
                            ->label('Total Submissions')
                            ->state(fn ($record) => $record->submissions()->count()),
                        Infolists\Components\TextEntry::make('completed_submissions')
                            ->label('Completed Submissions')
                            ->state(fn ($record) => $record->submissions()->where('status', 'submitted')->count()),
                    ])
                    ->columns(3),
            ]);
    }
}
