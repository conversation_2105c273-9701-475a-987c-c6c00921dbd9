<?php
    $record = $getRecord();
    $assignments = $record->assignments()->where('is_active', true)->get();
    $user = auth()->user();
?>

<!--[if BLOCK]><![endif]--><?php if($assignments->count() > 0): ?>
    <div class="space-y-4">
        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $assignments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $assignment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
                $userSubmission = $assignment->userSubmission($user->id);
                $canSubmit = $assignment->canUserSubmit($user->id);
                $isOverdue = $assignment->isOverdue();
                $questionsCount = $assignment->questions()->count();
            ?>
            
            <div class="bg-white dark:bg-gray-900 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                <!-- Assignment Header -->
                <div class="px-6 py-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 border-b border-gray-200 dark:border-gray-600">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                <?php echo e($assignment->title); ?>

                            </h3>
                            <!--[if BLOCK]><![endif]--><?php if($assignment->description): ?>
                                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                                    <?php echo e($assignment->description); ?>

                                </p>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                        
                        <!-- Assignment Status Badge -->
                        <div class="ml-4 flex flex-col items-end space-y-2">
                            <!--[if BLOCK]><![endif]--><?php if($userSubmission && $userSubmission->status === 'submitted'): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    Completed
                                </span>
                            <?php elseif($userSubmission && $userSubmission->status === 'in_progress'): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                    </svg>
                                    In Progress
                                </span>
                            <?php elseif($isOverdue): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                    </svg>
                                    Overdue
                                </span>
                            <?php elseif($canSubmit): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                    </svg>
                                    Available
                                </span>
                            <?php else: ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    Unavailable
                                </span>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>
                </div>

                <!-- Assignment Details -->
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <!-- Questions Count -->
                        <div class="flex items-center space-x-2">
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-gray-600 dark:text-gray-400">
                                <?php echo e($questionsCount); ?> <?php echo e(Str::plural('Question', $questionsCount)); ?>

                            </span>
                        </div>

                        <!-- Time Limit -->
                        <!--[if BLOCK]><![endif]--><?php if($assignment->time_limit): ?>
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span class="text-gray-600 dark:text-gray-400">
                                    <?php echo e($assignment->time_limit); ?> minutes
                                </span>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!-- Due Date -->
                        <!--[if BLOCK]><![endif]--><?php if($assignment->due_date): ?>
                            <div class="flex items-center space-x-2">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                                <span class="text-gray-600 dark:text-gray-400 <?php echo e($isOverdue ? 'text-red-600 dark:text-red-400' : ''); ?>">
                                    Due: <?php echo e($assignment->due_date->format('M j, Y g:i A')); ?>

                                </span>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>

                    <!-- Assignment Guide Preview -->
                    <!--[if BLOCK]><![endif]--><?php if($assignment->guide): ?>
                        <div class="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Instructions:</h4>
                            <div class="text-sm text-gray-600 dark:text-gray-400 prose prose-sm max-w-none">
                                <?php echo Str::limit(strip_tags($assignment->guide), 200); ?>

                            </div>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <!-- User Score (if completed) -->
                    <!--[if BLOCK]><![endif]--><?php if($userSubmission && $userSubmission->status === 'submitted' && $userSubmission->percentage !== null): ?>
                        <div class="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                            <div class="flex items-center justify-between">
                                <span class="text-sm font-medium text-green-800 dark:text-green-200">Your Score:</span>
                                <span class="text-lg font-bold text-green-800 dark:text-green-200">
                                    <?php echo e(number_format($userSubmission->percentage, 1)); ?>%
                                </span>
                            </div>
                            <div class="text-xs text-green-600 dark:text-green-400 mt-1">
                                <?php echo e($userSubmission->score); ?>/<?php echo e($userSubmission->total_points); ?> points
                            </div>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <!-- Action Buttons -->
                    <div class="mt-4 flex space-x-3">
                        <!--[if BLOCK]><![endif]--><?php if($canSubmit && $questionsCount > 0): ?>
                            <button 
                                onclick="startAssignment('<?php echo e($assignment->id); ?>')"
                                class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                            >
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <?php echo e($userSubmission && $userSubmission->status === 'in_progress' ? 'Continue Assignment' : 'Start Assignment'); ?>

                            </button>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!--[if BLOCK]><![endif]--><?php if($userSubmission && $userSubmission->status === 'submitted' && $assignment->show_results_immediately): ?>
                            <button 
                                onclick="viewResults('<?php echo e($assignment->id); ?>')"
                                class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                            >
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                View Results
                            </button>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <button 
                            onclick="viewAssignmentDetails('<?php echo e($assignment->id); ?>')"
                            class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                        >
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            View Details
                        </button>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    <?php $__env->startPush('scripts'); ?>
    <script>
        function startAssignment(assignmentId) {
            // This would redirect to the assignment taking page
            window.location.href = `/assignments/${assignmentId}/take`;
        }

        function viewResults(assignmentId) {
            // This would redirect to the results page
            window.location.href = `/assignments/${assignmentId}/results`;
        }

        function viewAssignmentDetails(assignmentId) {
            // This would open assignment details modal or page
            window.location.href = `/assignments/${assignmentId}`;
        }
    </script>
    <?php $__env->stopPush(); ?>
<?php else: ?>
    <div class="text-center py-8">
        <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No assignments available</h3>
        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">There are currently no assignments for this educational material.</p>
    </div>
<?php endif; ?><!--[if ENDBLOCK]><![endif]-->
<?php /**PATH C:\laragon\www\kapuas-bps-6171\resources\views/filament/infolists/education-assignments.blade.php ENDPATH**/ ?>