<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('indicators', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('aspect_id')->references('id')->on('aspects')
                ->onUpdate('cascade')
                ->onDelete('cascade');
            $table->string('name');
            $table->float('indicator_weight', 8, 4)->default(0);
            $table->float('relative_weight', 8, 4)->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('indicators');
    }
};
