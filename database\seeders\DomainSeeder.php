<?php

namespace Database\Seeders;

use App\Models\Aspect;
use App\Models\Domain;
use App\Models\Indicator;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DomainSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $domains = [
            [
                // DOMAIN 1
                [
                    "name" => "Prinsip Satu Data Indonesia",
                    "weight" => 0.28,
                    "aspect" => [
                        [
                            "name" => "Standar Data Statistik",
                            "weight" => 0.25,
                            "indicator" => [
                                [
                                    "name" => "Penerapan Standar Data Statistik",
                                    'indicator_weight' => 1,
                                    'relative_weight' => 0.07,
                                ]
                            ]
                        ],
                        [
                            "name" => "Metadata Statistik",
                            "weight" => 0.25,
                            "indicator" => [
                                [
                                    "name" => "Penerapan Metadata Statistik",
                                    'indicator_weight' => 1,
                                    'relative_weight' => 0.07,
                                ]
                            ]
                        ],
                        [
                            "name" => "Interoperabilitas Data",
                            "weight" => 0.25,
                            "indicator" => [
                                [
                                    "name" => "Penerapan Interoperabilitas Data",
                                    'indicator_weight' => 1,
                                    'relative_weight' => 0.07,
                                ]
                            ]
                        ],
                        [
                            "name" => "Kode Referensi dan/atau Data Induk",
                            "weight" => 0.25,
                            "indicator" => [
                                [
                                    "name" => "Penerapan Kode Referensi",
                                    'indicator_weight' => 1,
                                    'relative_weight' => 0.07,
                                ]
                            ]
                        ]
                    ],
                ],

                // DOMAIN 2
                [
                    "name" => "Kualitas Data",
                    "weight" => 0.24,
                    "aspect" => [
                        [
                            "name" => "Relevansi",
                            "weight" => 0.21,
                            "indicator" => [
                                [
                                    "name" => "Relevansi Data Terhadap Pengguna",
                                    'indicator_weight' => 0.6,
                                    'relative_weight' => 0.0302,
                                ],
                                [
                                    "name" => "Proses Identifikasi Kebutuhan Data",
                                    'indicator_weight' => 0.4,
                                    'relative_weight' => 0.0202,
                                ]
                            ]
                        ],
                        [
                            "name" => "Akurasi",
                            "weight" => 0.16,
                            "indicator" => [
                                [
                                    "name" => "Penilaian Akurasi Data",
                                    'indicator_weight' => 1,
                                    'relative_weight' => 0.0384,
                                ]
                            ]
                        ],
                        [
                            "name" => "Aktualitas & Ketepatan Waktu",
                            "weight" => 0.21,
                            "indicator" => [
                                [
                                    "name" => "Penjaminan Aktualitas Data",
                                    'indicator_weight' => 0.5,
                                    'relative_weight' => 0.0252,
                                ],
                                [
                                    "name" => "Pemantauan Ketepatan Waktu Diseminasi",
                                    'indicator_weight' => 0.5,
                                    'relative_weight' => 0.0252,
                                ]
                            ]
                        ],
                        [
                            "name" => "Aksesibilitas",
                            "weight" => 0.21,
                            "indicator" => [
                                [
                                    "name" => "Ketersediaan Data untuk Pengguna Data",
                                    'indicator_weight' => 0.34,
                                    'relative_weight' => 0.0171,
                                ],
                                [
                                    "name" => "Akses Media Penyebarluasan Data",
                                    'indicator_weight' => 0.33,
                                    'relative_weight' => 0.0166,
                                ],
                                [
                                    "name" => "Penyediaan Format Data",
                                    'indicator_weight' => 0.33,
                                    'relative_weight' => 0.0166,
                                ]
                            ]
                        ],
                        [
                            "name" => "Keterbandingan & Konsistensi",
                            "weight" => 0.21,
                            "indicator" => [
                                [
                                    "name" => "Keterbandingan Data",
                                    'indicator_weight' => 0.5,
                                    'relative_weight' => 0.0252,
                                ],
                                [
                                    "name" => "Konsistensi Statistik",
                                    'indicator_weight' => 0.5,
                                    'relative_weight' => 0.0252,
                                ]
                            ]
                        ]
                    ],
                ],

                // DOMAIN 3
                [
                    "name" => "Proses Bisnis Statistik",
                    "weight" => 0.19,
                    "aspect" => [
                        [
                            "name" => "Perencanaan Data",
                            "weight" => 0.32,
                            "indicator" => [
                                [
                                    "name" => "Pendefinisian Kebutuhan Statistik",
                                    'indicator_weight' => 0.33,
                                    'relative_weight' => 0.0201,
                                ],
                                [
                                    "name" => "Desain Statistik",
                                    'indicator_weight' => 0.33,
                                    'relative_weight' => 0.0201,
                                ],
                                [
                                    "name" => "Penyiapan Instrumen",
                                    'indicator_weight' => 0.34,
                                    'relative_weight' => 0.0207,
                                ]
                            ]
                        ],
                        [
                            "name" => "Pengumpulan Data",
                            "weight" => 0.26,
                            "indicator" => [
                                [
                                    "name" => "Proses Pengumpulan Data/Akuisisi Data",
                                    'indicator_weight' => 1,
                                    'relative_weight' => 0.0494,
                                ]
                            ]
                        ],
                        [
                            "name" => "Pemeriksaan Data",
                            "weight" => 0.21,
                            "indicator" => [
                                [
                                    "name" => "Pengolahan Data",
                                    'indicator_weight' => 0.5,
                                    'relative_weight' => 0.02,
                                ],
                                [
                                    "name" => "Analisis Data",
                                    'indicator_weight' => 0.5,
                                    'relative_weight' => 0.02,
                                ]
                            ]
                        ],
                        [
                            "name" => "Penyebarluasan Data",
                            "weight" => 0.21,
                            "indicator" => [
                                [
                                    "name" => "Diseminasi Data",
                                    'indicator_weight' => 1,
                                    'relative_weight' => 0.0399,
                                ],
                            ]
                        ],
                    ],
                ],

                // DOMAIN 4
                [
                    "name" => "Kelembagaan",
                    "weight" => 0.17,
                    "aspect" => [
                        [
                            "name" => "Profesionalitas",
                            "weight" => 0.35,
                            "indicator" => [
                                [
                                    "name" => "Penjaminan Transparansi Informasi Statistik",
                                    'indicator_weight' => 0.25,
                                    'relative_weight' => 0.0149,
                                ],
                                [
                                    "name" => "Penjaminan Netralitas dan Objektivitas terhadap Penggunaan Sumber Data dan Metodologi",
                                    'indicator_weight' => 0.25,
                                    'relative_weight' => 0.0149,
                                ],
                                [
                                    "name" => "Penjaminan Kualitas Data",
                                    'indicator_weight' => 0.25,
                                    'relative_weight' => 0.0149,
                                ],
                                [
                                    "name" => "Penjaminan Konfidensialitas Data",
                                    'indicator_weight' => 0.25,
                                    'relative_weight' => 0.0149,
                                ]
                            ]
                        ],
                        [
                            "name" => "SDM yang Memadai dan Kapabel",
                            "weight" => 0.30,
                            "indicator" => [
                                [
                                    "name" => "Pemenuhan Kompetensi SDM Bidang Statistik",
                                    'indicator_weight' => 0.5,
                                    'relative_weight' => 0.0255,
                                ],
                                [
                                    "name" => "Pemenuhan Kompetensi SDM Bidang Manajemen Data",
                                    'indicator_weight' => 0.5,
                                    'relative_weight' => 0.0255,
                                ]
                            ]
                        ],
                        [
                            "name" => "Pengorganisasian Statistik",
                            "weight" => 0.35,
                            "indicator" => [
                                [
                                    "name" => "Kolaborasi Penyelenggaraan Kegiatan Statistik",
                                    'indicator_weight' => 0.25,
                                    'relative_weight' => 0.0149,
                                ],
                                [
                                    "name" => "Penyelenggaraan Forum Satu Data Indonesia",
                                    'indicator_weight' => 0.25,
                                    'relative_weight' => 0.0149,
                                ],
                                [
                                    "name" => "Kolaborasi dengan Pembina Data Statistik",
                                    'indicator_weight' => 0.25,
                                    'relative_weight' => 0.0149,
                                ],
                                [
                                    "name" => "Pelaksanaan Tugas Sebagai Walidata",
                                    'indicator_weight' => 0.25,
                                    'relative_weight' => 0.0149,
                                ]
                            ]
                        ],
                    ],
                ],

                // DOMAIN 5
                [
                    "name" => "Statistik Nasional",
                    "weight" => 0.12,
                    "aspect" => [
                        [
                            "name" => "Pemanfaatan Data Statistik",
                            "weight" => 0.34,
                            "indicator" => [
                                [
                                    "name" => "Penggunaan Data Statistik Dasar untuk Perencanaan, Monitoring, dan Evaluasi, dan atau Penyusunan Kebijakan",
                                    'indicator_weight' => 0.34,
                                    'relative_weight' => 0.0139,
                                ],
                                [
                                    "name" => "Penggunaan Data Statistik Sektoral untuk Perencanaan, Monitoring, dan Evaluasi, dan atau Penyusunan Kebijakan",
                                    'indicator_weight' => 0.33,
                                    'relative_weight' => 0.0135,
                                ],
                                [
                                    "name" => "Sosialisasi dan Literasi Hasil Statistik",
                                    'indicator_weight' => 0.33,
                                    'relative_weight' => 0.0135,
                                ]
                            ]
                        ],
                        [
                            "name" => "Pengelolaan Kegiatan Statistik",
                            "weight" => 0.33,
                            "indicator" => [
                                [
                                    "name" => "Pelaksanaan Rekomendasi Kegiatan Statistik",
                                    'indicator_weight' => 1,
                                    'relative_weight' => 0.0396,
                                ]
                            ]
                        ],
                        [
                            "name" => "Penguatan SSN Berkelanjutan",
                            "weight" => 0.33,
                            "indicator" => [
                                [
                                    "name" => "Perencanaan Pembangunan Statistik",
                                    'indicator_weight' => 0.33,
                                    'relative_weight' => 0.0131,
                                ],
                                [
                                    "name" => "Penyebarluasan Data",
                                    'indicator_weight' => 0.33,
                                    'relative_weight' => 0.0131,
                                ],
                                [
                                    "name" => "Pemanfaatan Big Data",
                                    'indicator_weight' => 0.34,
                                    'relative_weight' => 0.0135,
                                ]
                            ]
                        ],
                    ],
                ],
            ]
        ];

        foreach ($domains as $domain) {
            foreach ($domain as $domain_data) {
                $domain = Domain::create([
                    'name' => $domain_data['name'],
                    'weight' => $domain_data['weight'],
                ]);

                foreach ($domain_data['aspect'] as $aspect_data) {
                    $aspect = Aspect::create([
                        'name' => $aspect_data['name'],
                        'weight' => $aspect_data['weight'],
                        'domain_id' => $domain->id,
                    ]);

                    foreach ($aspect_data['indicator'] as $indicator_data) {
                        Indicator::create([
                            'name' => $indicator_data['name'],
                            'indicator_weight' => $indicator_data['indicator_weight'],
                            'relative_weight' => $indicator_data['relative_weight'],
                            'aspect_id' => $aspect->id,
                        ]);
                    }
                }
            }
        }
    }
}
