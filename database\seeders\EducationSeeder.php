<?php

namespace Database\Seeders;

use App\Models\Domain;
use App\Models\Education;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class EducationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all domains
        $domains = Domain::all();

        if ($domains->isEmpty()) {
            $this->command->info('No domains found. Please run DomainSeeder first.');
            return;
        }

        $educationData = [
            [
                'title' => 'Introduction to One Data Indonesia Principles',
                'description' => 'Learn about the fundamental principles of One Data Indonesia and how it applies to statistical data management.',
                'youtube_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', // Sample YouTube URL
            ],
            [
                'title' => 'Statistical Data Quality Management',
                'description' => 'Understanding the importance of data quality in statistical processes and how to maintain high standards.',
                'youtube_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            ],
            [
                'title' => 'Data Integration and Interoperability',
                'description' => 'Best practices for integrating data from multiple sources and ensuring interoperability.',
                'youtube_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            ],
            [
                'title' => 'Statistical Infrastructure Development',
                'description' => 'Building robust statistical infrastructure to support data collection and analysis.',
                'youtube_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            ],
        ];

        foreach ($domains->take(4) as $index => $domain) {
            if (isset($educationData[$index])) {
                Education::create([
                    'domain_id' => $domain->id,
                    'title' => $educationData[$index]['title'],
                    'description' => $educationData[$index]['description'],
                    'youtube_url' => $educationData[$index]['youtube_url'],
                ]);
            }
        }
    }
}
