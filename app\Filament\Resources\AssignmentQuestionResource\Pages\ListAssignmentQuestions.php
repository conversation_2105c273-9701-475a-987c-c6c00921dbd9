<?php

namespace App\Filament\Resources\AssignmentQuestionResource\Pages;

use App\Filament\Resources\AssignmentQuestionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAssignmentQuestions extends ListRecords
{
    protected static string $resource = AssignmentQuestionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
