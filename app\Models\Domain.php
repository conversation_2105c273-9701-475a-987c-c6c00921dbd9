<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Domain extends Model
{
    use HasFactory, HasUuids;

    protected $guarded = ['id'];

    public function aspects()
    {
        return $this->hasMany(Aspect::class);
    }

    public function educations()
    {
        return $this->hasMany(Education::class);
    }
}
